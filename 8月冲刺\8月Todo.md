# Part1 - CDD 营销内容


# Part2 - TPF 技术验证
1. 魔云腾SuperSDK 跑通
   SuperSDK-解决多台云手机的创建和管理。
2. 魔云腾 RPASDK 跑通
   RPASDK - 解决单台云手机内APP具体的操作
3. Clash 二开 跑通
  可以远程设置链式代理,从而及时更新设备IP

# Part3 -- 产品原型
1. 批量注册后台  - 类TK云大师



# 8.1 Todo
1. 魔云腾固件升级     ✅
2. 魔云腾SDK和RPASDK  ✅
3. 手动配置链式代理

# 8.2 Todo
1. 思考CDD内容思路
2. 思考 Google Sites做 魔法的CPS?

# 8.5 Todo
1. SEO站群的思考  ✅

# 8.6 Todo
1. 思考号群和站群  ✅
2. 确定8月CDD内容列表 ✅
3. 手动配置链式代理  ✅

# 8.7 Todo
1. 买谷歌账号登录测试 可行   ✅
2. 海外APP 授权注册  可行   ✅
3. 思考Gmail账号问题 买OR自己注册   ✅
   魔云腾镜像是无法无手机号验证码注册GmaiL
4. 谷歌企业邮箱 注册TK Gmail 测试   ✅

# 8.8 Todo
1. 了解YoutubeShorts 变现逻辑 ✅
2. 思考号方向的生意   
    判断 1: 注册账部分产品化不是个好决策
           -1 注册号的资源是个难点
           -2 风控是个大问题
           
         2: 号+工具看起来更适合产品化。

    判断  出海云真机是真实手机
             把真机换为魔云腾思考？
   
    判断  不管是做号的批量注册
          还是管理账号   clash/neko 的二开是个必选项
    
# 8.9 Todo
1. 中高端货盘测试小红书  
   海宁等ODM公司有大量最新的款
   这些款+内容是根本竞争力        ✅
2. 草Agent产品化  
   工厂做电商平台的图片拍摄需求    ✅

# 8.10 Todo
1. 出海云真机产品思考
   -1 用户反馈测，有1个准用户需求
      判断是流量池太小，触达到有需求的用户有限
   -2 引流产品
      TK+  对手机的语言和SIM卡Code和IP 综合判断 来识别手机环境问题，以及对应修改。
      这个就可以吸引TK小B，纯引流
   -3 产品化
      基于nekobox/clash的二开部分是确定性需要开发的

2. 思考号+工具产品
   -1 基于Youtube 的产品
      产品侧是 YouTube号+后台
      流量侧是 YouTubeshorts教程内容
   
   -2 基于TikTok 的产品
       TikToK 号+后台  1个号月150元

3. 思考 号+数据产品
   Reddit广告数据平台

4. 魔云腾号的情况
   -FB INS reddit 运行良好
   -谷歌企业邮箱 TK封号  个人邮箱 正常

# 8.11 Todo
1. 反向代购生意 AcBuy 调研
   AcBuy月实收5千万  毛利40%  
   欧洲市场  快递8元/单
   流量：推测为Reddit 和 KOL
   -1 AcBuy调研
   -2 aliprice 插件代购生意调研
   -3 反向海淘生意调研

2. Dropshiping生意调研
   teemdrop 
   货1688 淘宝
   日500单
   流量：推测为google 关键词和FB信息流

3. Reddit调研
    调研Reddit 营销核心逻辑


